import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Rate limiting store (in-memory for demonstration, use Redis in production)
const rateLimit = new Map<string, { count: number; resetTime: number }>()

export async function middleware(req: NextRequest) {
  const response = NextResponse.next()

  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  // Add Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://*.supabase.co http://localhost:8765 https://devdb.syncrobit.net",
    "frame-ancestors 'none'"
  ].join('; ')
  response.headers.set('Content-Security-Policy', csp)

  // Check if authentication is enabled
  const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
  
  // If auth is disabled, return with security headers only
  if (!isAuthEnabled) {
    return response
  }

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/auth/login',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/_next',
    '/favicon.ico',
    '/logo.svg',
    '/api/health',
    '/images'
  ]

  // Check if current path is public
  const isPublicRoute = publicRoutes.some(route => 
    req.nextUrl.pathname.startsWith(route)
  )

  // Rate limiting for auth endpoints - DISABLED for internal tool
  // if (req.nextUrl.pathname.startsWith('/auth/')) {
  //   const clientIP = req.ip || req.headers.get('x-forwarded-for') || 'unknown'
  //   const key = `auth_${clientIP}`
  //   const now = Date.now()
  //   const windowMs = 15 * 60 * 1000 // 15 minutes
  //   const maxAttempts = 5

  //   const current = rateLimit.get(key)
    
  //   if (current && current.resetTime > now) {
  //     if (current.count >= maxAttempts) {
  //       return new NextResponse('Too Many Requests', { 
  //         status: 429,
  //         headers: {
  //           'Retry-After': Math.ceil((current.resetTime - now) / 1000).toString()
  //         }
  //       })
  //     }
  //     rateLimit.set(key, { count: current.count + 1, resetTime: current.resetTime })
  //   } else {
  //     rateLimit.set(key, { count: 1, resetTime: now + windowMs })
  //   }

  //   // Clean up expired entries
  //   for (const [entryKey, entry] of rateLimit.entries()) {
  //     if (entry.resetTime <= now) {
  //       rateLimit.delete(entryKey)
  //     }
  //   }
  // }

  // Session validation for Supabase tokens
  const supabaseAuthCookie = req.cookies.get('sb-auth')
  
  // If trying to access auth pages and we have a supabase session cookie, redirect to dashboard
  if (supabaseAuthCookie && req.nextUrl.pathname.startsWith('/auth/')) {
    const redirectUrl = req.nextUrl.clone()
    redirectUrl.pathname = '/'
    return NextResponse.redirect(redirectUrl)
  }

  // If trying to access protected routes without authentication, redirect to login
  if (!isPublicRoute && !supabaseAuthCookie) {
    const redirectUrl = req.nextUrl.clone()
    redirectUrl.pathname = '/auth/login'
    redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Session timeout check (30 minutes idle)
  if (supabaseAuthCookie && !isPublicRoute) {
    const sessionCookie = req.cookies.get('session-timestamp')
    const sessionTimestamp = sessionCookie ? parseInt(sessionCookie.value) : 0
    const now = Date.now()
    const thirtyMinutes = 30 * 60 * 1000
    
    if (sessionTimestamp && (now - sessionTimestamp) > thirtyMinutes) {
      // Session expired due to inactivity
      const redirectUrl = req.nextUrl.clone()
      redirectUrl.pathname = '/auth/login'
      redirectUrl.searchParams.set('reason', 'session_timeout')
      
      const response = NextResponse.redirect(redirectUrl)
      // Clear session cookies
      response.cookies.delete('sb-auth')
      response.cookies.delete('session-timestamp')
      return response
    }
    
    // Update session timestamp
    response.cookies.set('session-timestamp', now.toString(), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 8 * 60 * 60 // 8 hours max session
    })
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}